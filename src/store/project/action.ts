import { create } from 'zustand';

import {
  getProjects,
  createProject as create<PERSON>roject<PERSON><PERSON>,
  createProjectFromTemplate as createProject<PERSON>romTemplateAPI,
  createProjectFromOrder as createProjectFromOrderAPI,
  getProject,
  updateProjectActive as updateProject<PERSON>ctiveAPI,
} from '@/actions/project';
import {
  ProjectActions,
  ProjectStates,
  ProjectType,
  CreateProjectRequest,
  CreateProjectFromTemplateRequest,
  CreateProjectFromOrderRequest,
  UpdateProjectActiveRequest,
} from '@/store/project/type';

export const useProjectStore = create<ProjectStates & ProjectActions>(set => ({
  projects: [],
  selectedProject: null,
  loading: false,
  creating: false,
  creatingFromTemplate: false,
  creatingFromOrder: false,
  updatingActive: false,
  setProjects: (projects: ProjectType[]) => set(() => ({ projects })),
  setSelectedProject: (project: ProjectType | null) =>
    set(() => ({ selectedProject: project })),
  setLoading: (loading: boolean) => set(() => ({ loading })),
  setCreating: (creating: boolean) => set(() => ({ creating })),
  setCreatingFromTemplate: (creatingFromTemplate: boolean) =>
    set(() => ({ creatingFromTemplate })),
  setCreatingFromOrder: (creatingFromOrder: boolean) =>
    set(() => ({ creatingFromOrder })),
  setUpdatingActive: (updatingActive: boolean) =>
    set(() => ({ updatingActive })),
  fetchProjects: async (filters?: {
    type?: string;
    is_active?: boolean;
    cluster_id?: number;
    workspace_id?: number;
  }) => {
    try {
      set(() => ({ loading: true }));
      const response: any = await getProjects(filters);
      if (response?.status) {
        set(() => ({ projects: response.data, loading: false }));
      } else {
        set(() => ({ projects: [], loading: false }));
      }
    } catch (error) {
      console.error('Failed to fetch projects:', error);
      set(() => ({ projects: [], loading: false }));
    }
  },
  fetchProject: async (id: number) => {
    try {
      set(() => ({ loading: true }));
      const response: any = await getProject(id);
      if (response?.status) {
        set(() => ({ selectedProject: response.data, loading: false }));
      } else {
        set(() => ({ selectedProject: null, loading: false }));
      }
    } catch (error) {
      console.error('Failed to fetch project:', error);
      set(() => ({ selectedProject: null, loading: false }));
    }
  },
  createProject: async (data: CreateProjectRequest) => {
    try {
      set(() => ({ creating: true }));
      const response = await createProjectAPI(data);
      set(() => ({ creating: false }));
      return response;
    } catch (error) {
      console.error('Failed to create project:', error);
      set(() => ({ creating: false }));
      throw error;
    }
  },
  createProjectFromTemplate: async (data: CreateProjectFromTemplateRequest) => {
    try {
      set(() => ({ creatingFromTemplate: true }));
      const response = await createProjectFromTemplateAPI(data);
      set(() => ({ creatingFromTemplate: false }));
      return response;
    } catch (error) {
      console.error('Failed to create project from template:', error);
      set(() => ({ creatingFromTemplate: false }));
      throw error;
    }
  },
  createProjectFromOrder: async (data: CreateProjectFromOrderRequest) => {
    try {
      set(() => ({ creatingFromOrder: true }));
      const response = await createProjectFromOrderAPI(data);
      set(() => ({ creatingFromOrder: false }));
      return response;
    } catch (error) {
      console.error('Failed to create project from order:', error);
      set(() => ({ creatingFromOrder: false }));
      throw error;
    }
  },
  updateProjectActive: async (id: number, data: UpdateProjectActiveRequest) => {
    try {
      set(() => ({ updatingActive: true }));
      const response = await updateProjectActiveAPI(id, data);
      set(() => ({ updatingActive: false }));
      return response;
    } catch (error) {
      console.error('Failed to update project active status:', error);
      set(() => ({ updatingActive: false }));
      throw error;
    }
  },
}));
