'use client';

import {
  ArrowLeft,
  Calendar,
  Clock,
  Container,
  Server,
  Globe,
  ExternalLink,
  Settings,
  Activity,
  Database,
  Network,
  Shield,
  Pencil,
  Plus,
  ChevronDown,
  Play,
  Trash2,
} from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { useEffect, useState, useMemo } from 'react';
import { Toaster, toast } from 'sonner';

import { ConfirmActionModal } from '@/components/confirm-action-modal';
import { ConfirmDeleteModal } from '@/components/confirm-delete-modal';
import { CreateDeploymentModal } from '@/components/main/deployment/create-deployment-modal';
import { CreateEnvironmentModal } from '@/components/main/deployment/create-environment-modal';
import { EditDeploymentModal } from '@/components/main/deployment/edit-deployment-modal';
import { EnvironmentList } from '@/components/main/deployment/environment-list';
import { CreateIngressModal } from '@/components/main/ingress/create-ingress-modal';
import { CreateIngressSpecModal } from '@/components/main/ingress/create-ingress-spec-modal';
import { EditIngressModal } from '@/components/main/ingress/edit-ingress-modal';
import { EditIngressSpecModal } from '@/components/main/ingress/edit-ingress-spec-modal';
import { MainHeader } from '@/components/main/layout/main-header';
import { CreateServiceModal } from '@/components/main/service/create-service-modal';
import { EditServiceModal } from '@/components/main/service/edit-service-modal';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { useDeploymentStore } from '@/store/deployment/action';
import { useIngressStore } from '@/store/ingress/action';
import { useIngressSpecStore } from '@/store/ingress-spec/action';
import { useOperationStore } from '@/store/operation/action';
import { useProjectStore } from '@/store/project/action';
import { useServiceStore } from '@/store/service/action';
import { calculateProjectStatus } from '@/utils/calculateProjectStatus';
import { getBadgeVariant } from '@/utils/getVariant';

export default function ProjectDetailPage() {
  const router = useRouter();
  const params = useParams();
  const projectId = parseInt(params.id as string);

  const {
    selectedProject,
    loading,
    fetchProject,
    updateProjectActive,
    updatingActive,
  } = useProjectStore();
  const { createOperation, loading: operationLoading } = useOperationStore();
  const { deleteDeployment } = useDeploymentStore();
  const { deleteService } = useServiceStore();
  const { deleteIngress } = useIngressStore();
  const { deleteIngressSpec } = useIngressSpecStore();
  const [isClient, setIsClient] = useState(false);
  const [editingDeployment, setEditingDeployment] = useState<number | null>(
    null
  );
  const [createEnvDeploymentId, setCreateEnvDeploymentId] = useState<
    number | null
  >(null);
  const [createDeploymentOpen, setCreateDeploymentOpen] = useState(false);
  const [deletingDeployment, setDeletingDeployment] = useState<{
    id: number;
    name: string;
  } | null>(null);
  const [expandedEnvSections, setExpandedEnvSections] = useState<Set<number>>(
    new Set()
  );
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [currentAction, setCurrentAction] = useState<{
    type: 'publish' | 'unpublish';
  } | null>(null);
  const [editingService, setEditingService] = useState<any | null>(null);
  const [editServiceModalOpen, setEditServiceModalOpen] = useState(false);
  const [deletingService, setDeletingService] = useState<{
    id: number;
    name: string;
  } | null>(null);
  const [createServiceDeployment, setCreateServiceDeployment] = useState<{
    id: number;
    name: string;
    container_port: number;
  } | null>(null);
  const [createIngressOpen, setCreateIngressOpen] = useState(false);
  const [editingIngress, setEditingIngress] = useState<any | null>(null);
  const [editIngressModalOpen, setEditIngressModalOpen] = useState(false);
  const [deletingIngress, setDeletingIngress] = useState<{
    id: number;
    name: string;
  } | null>(null);
  const [createIngressSpecOpen, setCreateIngressSpecOpen] = useState<{
    ingressId: number;
    ingressName: string;
  } | null>(null);
  const [editingIngressSpec, setEditingIngressSpec] = useState<any | null>(
    null
  );
  const [editIngressSpecModalOpen, setEditIngressSpecModalOpen] =
    useState(false);
  const [deletingIngressSpec, setDeletingIngressSpec] = useState<{
    id: number;
    host: string;
    path: string;
  } | null>(null);
  const [confirmActiveToggleOpen, setConfirmActiveToggleOpen] = useState(false);
  const [pendingActiveState, setPendingActiveState] = useState<boolean | null>(
    null
  );

  useEffect(() => {
    setIsClient(true);
    if (projectId) {
      fetchProject(projectId);
    }
  }, [projectId, fetchProject]);

  const handleToggleActive = (isActive: boolean) => {
    setPendingActiveState(isActive);
    setConfirmActiveToggleOpen(true);
  };

  const confirmToggleActive = async () => {
    if (pendingActiveState === null || !selectedProject) {
      return;
    }

    try {
      const response = await updateProjectActive(selectedProject.id, {
        is_active: pendingActiveState,
      });

      if (response?.status) {
        toast.success(
          `Project ${pendingActiveState ? 'activated' : 'deactivated'} successfully`
        );
        // Re-fetch project details to get updated data
        fetchProject(projectId);
      } else {
        toast.error('Failed to update project status');
      }
    } catch (error) {
      console.error('Error updating project status:', error);
      toast.error('Failed to update project status');
    } finally {
      setConfirmActiveToggleOpen(false);
      setPendingActiveState(null);
    }
  };

  const handleEditDeployment = (deploymentId: number) => {
    setEditingDeployment(deploymentId);
  };

  const handleEditSuccess = () => {
    // Re-fetch project details after successful edit
    fetchProject(projectId);
    setEditingDeployment(null);
  };

  const handleCreateEnvironment = (deploymentId: number) => {
    setCreateEnvDeploymentId(deploymentId);
  };

  const handleEnvironmentSuccess = () => {
    // Re-fetch project details after successful environment creation
    fetchProject(projectId);
  };

  const handleCreateDeployment = () => {
    setCreateDeploymentOpen(true);
  };

  const handleCreateDeploymentSuccess = () => {
    // Re-fetch project details after successful deployment creation
    fetchProject(projectId);
    setCreateDeploymentOpen(false);
  };

  const handleDeleteDeployment = (
    deploymentId: number,
    deploymentName: string
  ) => {
    setDeletingDeployment({ id: deploymentId, name: deploymentName });
  };

  const confirmDeleteDeployment = async () => {
    if (!deletingDeployment) {
      return;
    }

    try {
      const response = await deleteDeployment(deletingDeployment.id);
      if (response?.status) {
        toast.success('Deployment deleted successfully');
        setDeletingDeployment(null);
        // Re-fetch project details after successful deletion
        fetchProject(projectId);
      } else {
        toast.error('Failed to delete deployment');
      }
    } catch (error) {
      console.error('Error deleting deployment:', error);
      toast.error('Failed to delete deployment');
    }
  };

  const handleEditService = (service: any) => {
    setEditingService(service);
    setEditServiceModalOpen(true);
  };

  const handleServiceEditSuccess = () => {
    // Re-fetch project details after successful service edit
    fetchProject(projectId);
    setEditServiceModalOpen(false);
    setEditingService(null);
  };

  const handleDeleteService = (serviceId: number, serviceName: string) => {
    setDeletingService({ id: serviceId, name: serviceName });
  };

  const confirmDeleteService = async () => {
    if (!deletingService) {
      return;
    }

    try {
      const response = await deleteService(deletingService.id);
      if (response?.status) {
        toast.success('Service deleted successfully');
        setDeletingService(null);
        // Re-fetch project details after successful deletion
        fetchProject(projectId);
      } else {
        toast.error('Failed to delete service');
      }
    } catch (error) {
      console.error('Error deleting service:', error);
      toast.error('Failed to delete service');
    }
  };

  const handleCreateService = (
    deploymentId: number,
    deploymentName: string,
    containerPort: number
  ) => {
    setCreateServiceDeployment({
      id: deploymentId,
      name: deploymentName,
      container_port: containerPort,
    });
  };

  const handleCreateServiceSuccess = () => {
    // Re-fetch project details after successful service creation
    fetchProject(projectId);
    setCreateServiceDeployment(null);
  };

  const handleCreateIngress = () => {
    setCreateIngressOpen(true);
  };

  const handleCreateIngressSuccess = () => {
    // Re-fetch project details after successful ingress creation
    fetchProject(projectId);
    setCreateIngressOpen(false);
  };

  const handleEditIngress = (ingress: any) => {
    setEditingIngress(ingress);
    setEditIngressModalOpen(true);
  };

  const handleIngressEditSuccess = () => {
    // Re-fetch project details after successful ingress edit
    fetchProject(projectId);
    setEditIngressModalOpen(false);
    setEditingIngress(null);
  };

  const handleDeleteIngress = (ingressId: number, ingressName: string) => {
    setDeletingIngress({ id: ingressId, name: ingressName });
  };

  const confirmDeleteIngress = async () => {
    if (!deletingIngress) {
      return;
    }

    try {
      const response = await deleteIngress(deletingIngress.id);
      if (response?.status) {
        toast.success('Ingress deleted successfully');
        setDeletingIngress(null);
        // Re-fetch project details after successful deletion
        fetchProject(projectId);
      } else {
        toast.error('Failed to delete ingress');
      }
    } catch (error) {
      console.error('Error deleting ingress:', error);
      toast.error('Failed to delete ingress');
    }
  };

  const handleCreateIngressSpec = (ingressId: number, ingressName: string) => {
    setCreateIngressSpecOpen({ ingressId, ingressName });
  };

  const handleCreateIngressSpecSuccess = () => {
    // Re-fetch project details after successful ingress spec creation
    fetchProject(projectId);
    setCreateIngressSpecOpen(null);
  };

  const handleEditIngressSpec = (ingressSpec: any, ingressName: string) => {
    setEditingIngressSpec({ ...ingressSpec, ingressName });
    setEditIngressSpecModalOpen(true);
  };

  const handleIngressSpecEditSuccess = () => {
    // Re-fetch project details after successful ingress spec edit
    fetchProject(projectId);
    setEditIngressSpecModalOpen(false);
    setEditingIngressSpec(null);
  };

  const handleDeleteIngressSpec = (
    ingressSpecId: number,
    host: string,
    path: string
  ) => {
    setDeletingIngressSpec({ id: ingressSpecId, host, path });
  };

  const confirmDeleteIngressSpec = async () => {
    if (!deletingIngressSpec) {
      return;
    }

    try {
      const response = await deleteIngressSpec(deletingIngressSpec.id);
      if (response?.status) {
        toast.success('Ingress spec deleted successfully');
        setDeletingIngressSpec(null);
        // Re-fetch project details after successful deletion
        fetchProject(projectId);
      } else {
        toast.error('Failed to delete ingress spec');
      }
    } catch (error) {
      console.error('Error deleting ingress spec:', error);
      toast.error('Failed to delete ingress spec');
    }
  };

  const toggleEnvSection = (deploymentId: number) => {
    setExpandedEnvSections(prev =>
      prev.has(deploymentId)
        ? new Set([...prev].filter(id => id !== deploymentId))
        : new Set(prev.add(deploymentId))
    );
  };

  const handlePublish = () => {
    setCurrentAction({ type: 'publish' });
    setConfirmOpen(true);
  };

  const handleUnpublish = () => {
    setCurrentAction({ type: 'unpublish' });
    setConfirmOpen(true);
  };

  const confirmAction = async () => {
    if (!currentAction || !selectedProject) {
      return;
    }

    const { type } = currentAction;

    try {
      if (type === 'publish') {
        const success = await createOperation({
          cluster_id: selectedProject.cluster.id,
          namespace_id: projectId,
          method: 'apply',
        });

        if (success) {
          toast.success(
            'Operation created successfully - Project publishing initiated'
          );
          setTimeout(() => {
            fetchProject(projectId);
          }, 10000);
        } else {
          toast.error('Failed to create publish operation');
          return;
        }
      } else if (type === 'unpublish') {
        const success = await createOperation({
          cluster_id: selectedProject.cluster.id,
          namespace_id: projectId,
          method: 'destroy',
        });

        if (success) {
          toast.success(
            'Operation created successfully - Project destroy initiated'
          );
          setTimeout(() => {
            fetchProject(projectId);
          }, 10000);
        } else {
          toast.error('Failed to create destroy operation');
          return;
        }
      }

      fetchProject(projectId);
    } catch (error) {
      console.error('Error performing action:', error);
      toast.error('Failed to perform operation');
    } finally {
      setConfirmOpen(false);
      setCurrentAction(null);
    }
  };

  // Calculate project status using useMemo for performance
  const projectStatus = useMemo(() => {
    return calculateProjectStatus(selectedProject);
  }, [selectedProject]);

  if (!isClient) {
    return (
      <div className='flex flex-1 flex-col'>
        <MainHeader />
        <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
          <div className='h-96 bg-muted animate-pulse rounded-md flex items-center justify-center'>
            <p className='text-muted-foreground'>Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className='flex flex-1 flex-col'>
        <MainHeader />
        <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
          <div className='h-96 bg-muted animate-pulse rounded-md flex items-center justify-center'>
            <p className='text-muted-foreground'>Loading project details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!selectedProject) {
    return (
      <div className='flex flex-1 flex-col'>
        <MainHeader />
        <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
          <div className='flex items-center gap-4 mb-4'>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => router.push('/projects')}
              className='flex items-center gap-2'
            >
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <h1 className='text-3xl font-medium tracking-tight'>
              Project Not Found
            </h1>
          </div>
          <Card>
            <CardContent className='flex items-center justify-center py-12'>
              <p className='text-muted-foreground'>
                The requested project could not be found.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className='flex flex-1 flex-col'>
      <MainHeader />
      <Toaster />
      <div className='flex flex-1 flex-col gap-6 p-4 pt-0'>
        {/* Header Section */}
        <div className='flex items-center gap-4'>
          <Button
            variant='ghost'
            size='sm'
            onClick={() => router.push('/projects')}
            className='flex items-center gap-2'
          >
            <ArrowLeft className='h-4 w-4' />
          </Button>
          <div className='flex-1'>
            <div className='flex items-center gap-3 mb-2'>
              <h1 className='text-3xl font-medium tracking-tight'>
                {selectedProject.name}
              </h1>
              <Badge variant='outline'>{selectedProject.type}</Badge>
              {selectedProject.type !== 'template' &&
                projectStatus === 'active' && (
                  <div className='flex items-center gap-2'>
                    <Switch
                      checked={selectedProject.is_active}
                      onCheckedChange={handleToggleActive}
                      disabled={updatingActive}
                    />
                    <span className='text-sm text-muted-foreground'>
                      {selectedProject.is_active ? 'Active' : 'Inactive'}
                    </span>
                    {updatingActive && (
                      <div className='text-xs text-muted-foreground'>
                        Updating...
                      </div>
                    )}
                  </div>
                )}
            </div>
            <p className='text-muted-foreground'>
              {selectedProject.slug} • Created{' '}
              {new Date(selectedProject.created_at).toLocaleDateString()}
            </p>
          </div>
          <div className='flex items-center gap-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() =>
                router.push(`/logs?event_id=${projectId}&event=namespace`)
              }
            >
              <Activity className='mr-2 h-4 w-4' />
              Logs
            </Button>
            {selectedProject.cluster.status.id === 3 &&
              // projectStatus === 'active' &&
              selectedProject.type !== 'template' && (
                <Button
                  variant='outline'
                  size='sm'
                  onClick={() => router.push(`/projects/${projectId}/domains`)}
                >
                  <Globe className='mr-2 h-4 w-4' />
                  Domains
                </Button>
              )}
            {selectedProject.cluster.status.id === 3 &&
              selectedProject.type !== 'template' &&
              (projectStatus === 'maintenance' ||
                projectStatus === 'unpublished') && (
                <Button
                  variant='default'
                  size='sm'
                  onClick={handlePublish}
                  disabled={operationLoading}
                >
                  <Play className='mr-2 h-4 w-4' />
                  Publish
                </Button>
              )}
            {projectStatus === 'active' &&
              selectedProject.type !== 'template' && (
                <Button size='sm' onClick={() => handleUnpublish()}>
                  <Trash2 className='mr-2 h-4 w-4' />
                  Destroy
                </Button>
              )}
          </div>
        </div>

        {/* Project Overview Cards */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Deployments</CardTitle>
              <Container className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {selectedProject.deployments?.length || 0}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Services</CardTitle>
              <Server className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {selectedProject.services?.length || 0}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Ingress</CardTitle>
              <Globe className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {selectedProject.ingress?.length || 0}
              </div>
            </CardContent>
          </Card>
          {selectedProject.type !== 'template' && (
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Status</CardTitle>
                <Activity className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>
                  <Badge variant={getBadgeVariant(projectStatus)}>
                    {projectStatus}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
          {/* Left Column - Project Info & Cluster */}
          <div className='space-y-6'>
            {/* Project Information */}
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Settings className='h-5 w-5' />
                  Project Information
                </CardTitle>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='grid grid-cols-2 gap-4 text-sm'>
                  <div>
                    <span className='font-medium text-muted-foreground'>
                      Name:
                    </span>
                    <div className='font-medium'>{selectedProject.name}</div>
                  </div>
                  <div>
                    <span className='font-medium text-muted-foreground'>
                      Slug:
                    </span>
                    <div className='font-medium'>{selectedProject.slug}</div>
                  </div>
                  <div>
                    <span className='font-medium text-muted-foreground'>
                      Type:
                    </span>
                    <div>
                      <Badge variant='outline'>{selectedProject.type}</Badge>
                    </div>
                  </div>
                  {selectedProject.type !== 'template' && (
                    <div>
                      <span className='font-medium text-muted-foreground'>
                        Status:
                      </span>
                      <div>
                        <Badge variant={getBadgeVariant(projectStatus)}>
                          {projectStatus}
                        </Badge>
                      </div>
                    </div>
                  )}
                </div>
                <Separator />
                <div className='grid grid-cols-1 gap-4 text-sm'>
                  <div className='flex items-center gap-2'>
                    <Calendar className='h-4 w-4 text-muted-foreground' />
                    <span className='font-medium text-muted-foreground'>
                      Created:
                    </span>
                    <span>
                      {new Date(
                        selectedProject.created_at
                      ).toLocaleDateString()}
                    </span>
                  </div>
                  <div className='flex items-center gap-2'>
                    <Clock className='h-4 w-4 text-muted-foreground' />
                    <span className='font-medium text-muted-foreground'>
                      Updated:
                    </span>
                    <span>
                      {new Date(
                        selectedProject.updated_at
                      ).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Cluster Information */}
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Database className='h-5 w-5' />
                  Cluster Information
                </CardTitle>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='space-y-3'>
                  <div className='flex items-center justify-between'>
                    <span className='font-medium'>
                      {selectedProject.cluster.name}
                    </span>
                    <Badge
                      variant={getBadgeVariant(selectedProject.cluster.status)}
                    >
                      {selectedProject.cluster.status.name}
                    </Badge>
                  </div>
                  <div className='grid grid-cols-2 gap-4 text-sm'>
                    <div>
                      <span className='font-medium text-muted-foreground'>
                        Region:
                      </span>
                      <div>{selectedProject.cluster.region}</div>
                    </div>
                    <div>
                      <span className='font-medium text-muted-foreground'>
                        Size:
                      </span>
                      <div>{selectedProject.cluster.size}</div>
                    </div>
                    <div>
                      <span className='font-medium text-muted-foreground'>
                        Pool:
                      </span>
                      <div>{selectedProject.cluster.pool_name}</div>
                    </div>
                    <div>
                      <span className='font-medium text-muted-foreground'>
                        Nodes:
                      </span>
                      <div>{selectedProject.cluster.node_count}</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Deployments, Services, Ingress */}
          <div className='lg:col-span-2 space-y-6'>
            {/* Deployments */}
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center justify-between'>
                  <div className='flex items-center gap-2'>
                    <Container className='h-5 w-5' />
                    Deployments ({selectedProject.deployments?.length || 0})
                  </div>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={handleCreateDeployment}
                    className='flex items-center gap-2'
                  >
                    <Plus className='h-4 w-4' />
                    Add Deployment
                  </Button>
                </CardTitle>
              </CardHeader>
              {selectedProject.deployments &&
              selectedProject.deployments.length > 0 ? (
                <CardContent>
                  <div className='space-y-4'>
                    {selectedProject.deployments.map(deployment => (
                      <div
                        key={deployment.id}
                        className='border rounded-lg p-4 space-y-3'
                      >
                        <div className='flex items-center justify-between'>
                          <h3 className='font-medium'>{deployment.name}</h3>
                          <div className='flex items-center gap-2'>
                            {selectedProject.type !== 'template' && (
                              <Badge
                                variant={getBadgeVariant(deployment.status)}
                              >
                                {deployment.status.name}
                              </Badge>
                            )}
                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() =>
                                handleEditDeployment(deployment.id)
                              }
                              className='p-1 h-5 w-5'
                              title='Edit Deployment'
                            >
                              <Pencil className='h-2 w-2' />
                            </Button>
                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() =>
                                handleDeleteDeployment(
                                  deployment.id,
                                  deployment.name
                                )
                              }
                              className='p-1 h-5 w-5 text-destructive hover:text-destructive'
                              title='Delete Deployment'
                            >
                              <Trash2 className='h-2 w-2' />
                            </Button>
                          </div>
                        </div>

                        <div className='grid grid-cols-2 md:grid-cols-3 gap-4 text-sm'>
                          <div>
                            <span className='font-medium text-muted-foreground'>
                              Image:
                            </span>
                            <div className='font-mono text-xs bg-muted px-2 py-1 rounded mt-1'>
                              {deployment.image}
                            </div>
                          </div>
                          <div>
                            <span className='font-medium text-muted-foreground'>
                              Port:
                            </span>
                            <div>{deployment.container_port}</div>
                          </div>
                          <div>
                            <span className='font-medium text-muted-foreground'>
                              Replicas:
                            </span>
                            <div>{deployment.replicas}</div>
                          </div>
                        </div>

                        {/* Environment Variables Section */}
                        <Collapsible
                          open={expandedEnvSections.has(deployment.id)}
                          onOpenChange={() => toggleEnvSection(deployment.id)}
                        >
                          <div className='border rounded-lg bg-muted/30'>
                            <CollapsibleTrigger asChild>
                              <div className='flex items-center justify-between p-3 cursor-pointer hover:bg-muted/50 transition-colors'>
                                <div className='flex items-center gap-2'>
                                  <ChevronDown
                                    className={`h-4 w-4 text-muted-foreground transition-transform ${
                                      expandedEnvSections.has(deployment.id)
                                        ? 'rotate-180'
                                        : ''
                                    }`}
                                  />
                                  <span className='font-medium text-sm text-muted-foreground'>
                                    Environment Variables (
                                    {deployment.environments?.length || 0})
                                  </span>
                                </div>
                                <Button
                                  variant='ghost'
                                  size='sm'
                                  onClick={e => {
                                    e.stopPropagation();
                                    handleCreateEnvironment(deployment.id);
                                  }}
                                  className='h-6 w-6 p-0'
                                  title='Add Environment Variable'
                                >
                                  <Plus className='h-3 w-3' />
                                </Button>
                              </div>
                            </CollapsibleTrigger>
                            <CollapsibleContent className='px-3 pb-3'>
                              {deployment.environments &&
                              deployment.environments.length > 0 ? (
                                <EnvironmentList
                                  environments={deployment.environments}
                                  deploymentId={deployment.id}
                                  onSuccess={handleEnvironmentSuccess}
                                />
                              ) : (
                                <div className='text-sm text-muted-foreground italic bg-muted/30 rounded-lg p-4 text-center'>
                                  No environment variables configured
                                  <div className='text-xs mt-1'>
                                    Click the + button above to add your first
                                    environment variable
                                  </div>
                                </div>
                              )}
                            </CollapsibleContent>
                          </div>
                        </Collapsible>

                        {/* Service Creation Section */}
                        <div className='flex items-center justify-between pt-2 border-t'>
                          <span className='text-sm font-medium text-muted-foreground'>
                            Services
                          </span>
                          <Button
                            variant='outline'
                            size='sm'
                            onClick={() =>
                              handleCreateService(
                                deployment.id,
                                deployment.name,
                                deployment.container_port
                              )
                            }
                            className='flex items-center gap-2 h-7 text-xs'
                          >
                            <Plus className='h-3 w-3' />
                            Service
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              ) : (
                <CardContent>
                  <div className='text-center py-8'>
                    <Container className='h-12 w-12 mx-auto text-muted-foreground mb-4' />
                    <h3 className='text-lg font-medium mb-2'>No deployments</h3>
                    <p className='text-muted-foreground mb-4'>
                      Get started by creating your first deployment.
                    </p>
                    <Button
                      variant='default'
                      onClick={handleCreateDeployment}
                      className='flex items-center gap-2'
                    >
                      <Plus className='h-4 w-4' />
                      Create Deployment
                    </Button>
                  </div>
                </CardContent>
              )}
            </Card>

            {/* Services */}
            {selectedProject.services &&
              selectedProject.services.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className='flex items-center gap-2'>
                      <Network className='h-5 w-5' />
                      Services ({selectedProject.services.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className='space-y-4'>
                      {selectedProject.services.map(service => (
                        <div
                          key={service.id}
                          className='border rounded-lg p-4 space-y-3'
                        >
                          <div className='flex items-center justify-between'>
                            <h3 className='font-medium'>{service.name}</h3>
                            <div className='flex items-center gap-2'>
                              <>
                                <div className='flex items-center gap-1'>
                                  <Button
                                    variant='ghost'
                                    size='sm'
                                    onClick={() => handleEditService(service)}
                                    className='p-1 h-6 w-6'
                                  >
                                    <Pencil className='h-3 w-3' />
                                  </Button>
                                  <Button
                                    variant='ghost'
                                    size='sm'
                                    onClick={() =>
                                      handleDeleteService(
                                        service.id,
                                        service.name
                                      )
                                    }
                                    className='p-1 h-6 w-6 text-destructive hover:text-destructive'
                                  >
                                    <Trash2 className='h-3 w-3' />
                                  </Button>
                                </div>
                                {selectedProject.type !== 'template' && (
                                  <Badge
                                    variant={getBadgeVariant(service.status)}
                                  >
                                    {service.status.name}
                                  </Badge>
                                )}
                              </>
                            </div>
                          </div>

                          <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm'>
                            <div>
                              <span className='font-medium text-muted-foreground'>
                                Type:
                              </span>
                              <div>{service.type}</div>
                            </div>
                            <div>
                              <span className='font-medium text-muted-foreground'>
                                Ports:
                              </span>
                              <div>
                                {service.port} → {service.target_port}
                              </div>
                            </div>
                            {service.cluster_ip && (
                              <div>
                                <span className='font-medium text-muted-foreground'>
                                  Cluster IP:
                                </span>
                                <div className='font-mono text-xs'>
                                  {service.cluster_ip}
                                </div>
                              </div>
                            )}
                            {service.external_ip && (
                              <div>
                                <span className='font-medium text-muted-foreground'>
                                  External IP:
                                </span>
                                <div className='font-mono text-xs'>
                                  {service.external_ip}
                                </div>
                              </div>
                            )}
                          </div>

                          {service.ingress_specs &&
                            service.ingress_specs.length > 0 &&
                            ![1, 2, 5, 7, 9].includes(service.status.id) && (
                              <div>
                                <span className='font-medium text-sm text-muted-foreground'>
                                  Ingress Specs:
                                </span>
                                <div className='space-y-2 mt-2'>
                                  {service.ingress_specs.map(spec => (
                                    <div
                                      key={spec.id}
                                      className='flex items-center gap-2 text-sm bg-muted/30 rounded p-2'
                                    >
                                      <span className='text-blue-600 font-mono'>
                                        {spec.host}
                                        {spec.path}
                                      </span>
                                      <span className='text-muted-foreground'>
                                        :{spec.port}
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

            {/* Ingress */}
            <Card>
              <CardHeader>
                <div className='flex items-center justify-between'>
                  <CardTitle className='flex items-center gap-2'>
                    <Shield className='h-5 w-5' />
                    Ingress ({selectedProject.ingress?.length || 0})
                  </CardTitle>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={handleCreateIngress}
                    className='flex items-center gap-1'
                  >
                    <Plus className='h-4 w-4' />
                    Add Ingress
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {selectedProject.ingress &&
                selectedProject.ingress.length > 0 ? (
                  <div className='space-y-4'>
                    {selectedProject.ingress.map(ingress => (
                      <div
                        key={ingress.id}
                        className='border rounded-lg p-4 space-y-3'
                      >
                        <div className='flex items-center justify-between'>
                          <h3 className='font-medium'>{ingress.name}</h3>
                          <div className='flex items-center gap-2'>
                            <>
                              <div className='flex items-center gap-1'>
                                <Button
                                  variant='ghost'
                                  size='sm'
                                  onClick={() => handleEditIngress(ingress)}
                                  className='p-1 h-6 w-6'
                                >
                                  <Pencil className='h-3 w-3' />
                                </Button>
                                <Button
                                  variant='ghost'
                                  size='sm'
                                  onClick={() =>
                                    handleDeleteIngress(
                                      ingress.id,
                                      ingress.name
                                    )
                                  }
                                  className='p-1 h-6 w-6 text-destructive hover:text-destructive'
                                >
                                  <Trash2 className='h-3 w-3' />
                                </Button>
                              </div>
                              {selectedProject.type !== 'template' && (
                                <Badge
                                  variant={getBadgeVariant(ingress.status)}
                                >
                                  {ingress.status.name}
                                </Badge>
                              )}
                            </>
                          </div>
                        </div>

                        <div className='text-sm'>
                          <span className='font-medium text-muted-foreground'>
                            Class:{' '}
                          </span>
                          <span className='font-mono bg-muted px-2 py-1 rounded'>
                            {ingress.class}
                          </span>
                        </div>

                        <div>
                          <div className='flex items-center justify-between'>
                            <span className='font-medium text-sm text-muted-foreground'>
                              Routes:
                            </span>
                            <Button
                              variant='outline'
                              size='sm'
                              onClick={() =>
                                handleCreateIngressSpec(
                                  ingress.id,
                                  ingress.name
                                )
                              }
                              className='flex items-center gap-1 h-7 text-xs'
                            >
                              <Plus className='h-3 w-3' />
                              Add Route
                            </Button>
                          </div>
                          {ingress.ingress_specs &&
                          ingress.ingress_specs.length > 0 ? (
                            <div className='space-y-2 mt-2'>
                              {ingress.ingress_specs.map(spec => (
                                <div
                                  key={spec.id}
                                  className='bg-muted/30 rounded p-2'
                                >
                                  <div className='flex items-center justify-between text-sm'>
                                    <div className='flex items-center gap-2'>
                                      <a
                                        href={`https://${spec.host}${spec.path}`}
                                        target='_blank'
                                        rel='noopener noreferrer'
                                        className='text-blue-600 hover:text-blue-800 flex items-center gap-1 font-mono text-xs'
                                      >
                                        {spec.host}
                                        {spec.path}
                                        <ExternalLink className='h-3 w-3' />
                                      </a>
                                      <div className='flex items-center gap-2 text-xs text-muted-foreground'>
                                        <span>:{spec.port}</span>
                                        {spec.service && (
                                          <span>
                                            → {spec.service.name}:
                                            {spec.service.port}
                                          </span>
                                        )}
                                      </div>
                                    </div>
                                    <div className='flex items-center gap-1'>
                                      <Button
                                        variant='ghost'
                                        size='sm'
                                        onClick={() =>
                                          handleEditIngressSpec(
                                            spec,
                                            ingress.name
                                          )
                                        }
                                        className='p-1 h-5 w-5'
                                      >
                                        <Pencil className='h-2.5 w-2.5' />
                                      </Button>
                                      <Button
                                        variant='ghost'
                                        size='sm'
                                        onClick={() =>
                                          handleDeleteIngressSpec(
                                            spec.id,
                                            spec.host,
                                            spec.path
                                          )
                                        }
                                        className='p-1 h-5 w-5 text-destructive hover:text-destructive'
                                      >
                                        <Trash2 className='h-2.5 w-2.5' />
                                      </Button>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <div className='text-center py-4 text-muted-foreground mt-2'>
                              <Network className='h-8 w-8 mx-auto mb-2 opacity-50' />
                              <p className='text-xs'>No routes configured</p>
                              <p className='text-xs mt-1'>
                                Add a route to define how traffic reaches your
                                services
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className='text-center py-8 text-muted-foreground'>
                    <Shield className='h-12 w-12 mx-auto mb-4 opacity-50' />
                    <p className='text-sm'>No ingress configured</p>
                    <p className='text-xs mt-1'>
                      Create an ingress to manage external access to your
                      services
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Edit Deployment Modal */}
        {editingDeployment !== null && (
          <EditDeploymentModal
            deploymentId={editingDeployment}
            onSuccess={handleEditSuccess}
            onClose={() => setEditingDeployment(null)}
          />
        )}

        {/* Create Deployment Modal */}
        <CreateDeploymentModal
          namespaceId={projectId}
          open={createDeploymentOpen}
          onOpenChange={setCreateDeploymentOpen}
          onSuccess={handleCreateDeploymentSuccess}
        />

        {/* Create Environment Modal */}
        {createEnvDeploymentId !== null && (
          <CreateEnvironmentModal
            deploymentId={createEnvDeploymentId}
            open={createEnvDeploymentId !== null}
            onOpenChange={open => {
              if (!open) {
                setCreateEnvDeploymentId(null);
              }
            }}
            onSuccess={handleEnvironmentSuccess}
          />
        )}

        {/* Delete Deployment Confirmation Modal */}
        {deletingDeployment && (
          <ConfirmDeleteModal
            open={deletingDeployment !== null}
            onOpenChange={open => {
              if (!open) {
                setDeletingDeployment(null);
              }
            }}
            onConfirm={confirmDeleteDeployment}
            title='Delete Deployment'
            description={`Are you sure you want to delete the deployment "${deletingDeployment.name}"? This action cannot be undone and will remove all associated resources.`}
          />
        )}

        {/* Confirm Action Modal */}
        <ConfirmActionModal
          open={confirmOpen}
          onOpenChange={setConfirmOpen}
          onConfirm={confirmAction}
          title={
            currentAction?.type === 'publish'
              ? 'Publish Project'
              : 'Destroy Project'
          }
          description={
            currentAction?.type === 'publish'
              ? 'Are you sure you want to publish this project ?'
              : 'Are you sure you want to destroy this project ?'
          }
          variant={
            currentAction?.type === 'publish' ? 'default' : 'destructive'
          }
          icon={
            currentAction?.type === 'publish' ? (
              <Play className='h-5 w-5 text-blue-600' />
            ) : (
              <Trash2 className='h-5 w-5 text-red-600' />
            )
          }
        />

        {/* Edit Service Modal */}
        {editingService && (
          <EditServiceModal
            service={editingService}
            open={editServiceModalOpen}
            onOpenChange={setEditServiceModalOpen}
            onSuccess={handleServiceEditSuccess}
          />
        )}

        {/* Delete Service Modal */}
        {deletingService && (
          <ConfirmDeleteModal
            open={deletingService !== null}
            onOpenChange={open => {
              if (!open) {
                setDeletingService(null);
              }
            }}
            onConfirm={confirmDeleteService}
            title='Delete Service'
            description={`Are you sure you want to delete the service "${deletingService.name}"? This action cannot be undone and will remove all associated resources.`}
          />
        )}

        {/* Create Service Modal */}
        {createServiceDeployment && (
          <CreateServiceModal
            open={createServiceDeployment !== null}
            onOpenChange={open => {
              if (!open) {
                setCreateServiceDeployment(null);
              }
            }}
            deploymentId={createServiceDeployment.id}
            deploymentName={createServiceDeployment.name}
            deploymentContainerPort={createServiceDeployment.container_port}
            namespaceId={projectId}
            onSuccess={handleCreateServiceSuccess}
          />
        )}

        {/* Create Ingress Modal */}
        <CreateIngressModal
          open={createIngressOpen}
          onOpenChange={setCreateIngressOpen}
          namespaceId={projectId}
          onSuccess={handleCreateIngressSuccess}
        />

        {/* Edit Ingress Modal */}
        {editingIngress && (
          <EditIngressModal
            ingress={editingIngress}
            open={editIngressModalOpen}
            onOpenChange={setEditIngressModalOpen}
            onSuccess={handleIngressEditSuccess}
          />
        )}

        {/* Delete Ingress Modal */}
        {deletingIngress && (
          <ConfirmDeleteModal
            open={deletingIngress !== null}
            onOpenChange={open => {
              if (!open) {
                setDeletingIngress(null);
              }
            }}
            onConfirm={confirmDeleteIngress}
            title='Delete Ingress'
            description={`Are you sure you want to delete the ingress "${deletingIngress.name}"? This action cannot be undone and will remove all associated resources.`}
          />
        )}

        {/* Create Ingress Spec Modal */}
        {createIngressSpecOpen && (
          <CreateIngressSpecModal
            ingressId={createIngressSpecOpen.ingressId}
            ingressName={createIngressSpecOpen.ingressName}
            services={selectedProject.services || []}
            open={createIngressSpecOpen !== null}
            onOpenChange={open => {
              if (!open) {
                setCreateIngressSpecOpen(null);
              }
            }}
            onSuccess={handleCreateIngressSpecSuccess}
          />
        )}

        {/* Edit Ingress Spec Modal */}
        {editingIngressSpec && (
          <EditIngressSpecModal
            ingressSpec={editingIngressSpec}
            ingressName={editingIngressSpec.ingressName}
            services={selectedProject.services || []}
            open={editIngressSpecModalOpen}
            onOpenChange={setEditIngressSpecModalOpen}
            onSuccess={handleIngressSpecEditSuccess}
          />
        )}

        {/* Delete Ingress Spec Modal */}
        {deletingIngressSpec && (
          <ConfirmDeleteModal
            open={deletingIngressSpec !== null}
            onOpenChange={open => {
              if (!open) {
                setDeletingIngressSpec(null);
              }
            }}
            onConfirm={confirmDeleteIngressSpec}
            title='Delete Ingress Spec'
            description={`Are you sure you want to delete the route "${deletingIngressSpec.host}${deletingIngressSpec.path}"? This action cannot be undone.`}
          />
        )}

        {/* Project Active Toggle Confirmation Modal */}
        <ConfirmActionModal
          open={confirmActiveToggleOpen}
          onOpenChange={setConfirmActiveToggleOpen}
          onConfirm={confirmToggleActive}
          title={`${pendingActiveState ? 'Activate' : 'Deactivate'} Project`}
          description={`Are you sure you want to ${
            pendingActiveState ? 'activate' : 'deactivate'
          } the project "${selectedProject?.name}"? This will change the project's operational status.`}
          actionText={pendingActiveState ? 'Activate' : 'Deactivate'}
          loadingText={pendingActiveState ? 'Activating...' : 'Deactivating...'}
          variant={pendingActiveState ? 'default' : 'destructive'}
          icon={
            pendingActiveState ? (
              <Activity className='h-5 w-5 text-green-600' />
            ) : (
              <Activity className='h-5 w-5 text-red-600' />
            )
          }
        />
      </div>
    </div>
  );
}
