'use client';

import {
  Folder,
  Search,
  Trash2,
  ChevronDown,
  ChevronRight,
  Filter,
  ExternalLink,
  Server,
  Globe,
  Container,
  Plus,
  Cog,
  Activity,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Fragment, useState, useEffect, useCallback } from 'react';
import { Toaster, toast } from 'sonner';

import { ConfirmActionModal } from '@/components/confirm-action-modal';
import { MainHeader } from '@/components/main/layout/main-header';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useDebounce } from '@/hooks/use-debounce';
import { useClusterStore } from '@/store/cluster/action';
import { useNamespaceStore } from '@/store/namespace/action';
import { useProjectStore } from '@/store/project/action';
import { useWorkspaceStore } from '@/store/workspace/action';
import { calculateProjectStatus } from '@/utils/calculateProjectStatus';
import { getBadgeVariant } from '@/utils/getVariant';

export default function ProjectsPage() {
  const {
    projects,
    loading,
    fetchProjects,
    updateProjectActive,
    updatingActive,
    deleteProject,
    deleting,
  } = useProjectStore();
  const { clusters, fetchClusters } = useClusterStore();
  const { types: namespaceTypes, fetchTypes: fetchNamespaceTypes } =
    useNamespaceStore();
  const { fetchWorkspaces, selectedWorkspace } = useWorkspaceStore();
  const router = useRouter();

  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm, 500); // 500ms delay
  const [typeFilter, setTypeFilter] = useState<string>('');
  const [isActiveFilter, setIsActiveFilter] = useState<boolean | undefined>(
    undefined
  );
  const [clusterFilter, setClusterFilter] = useState<number | undefined>(
    undefined
  );
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set());
  const [isClient, setIsClient] = useState(false);
  const [confirmActiveToggleOpen, setConfirmActiveToggleOpen] = useState(false);
  const [pendingToggle, setPendingToggle] = useState<{
    projectId: number;
    projectName: string;
    isActive: boolean;
  } | null>(null);
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [deletingProject, setDeletingProject] = useState<{
    id: number;
    name: string;
  } | null>(null);

  const loadProjects = useCallback(() => {
    const filters: any = {};

    // Always include selectedWorkspace ID if available
    if (selectedWorkspace?.id) {
      filters.workspace_id = selectedWorkspace.id;
    }

    if (typeFilter) {
      filters.type = typeFilter;
    }
    if (isActiveFilter !== undefined) {
      filters.is_active = isActiveFilter;
    }
    if (clusterFilter) {
      filters.cluster_id = clusterFilter;
    }
    if (debouncedSearchTerm.trim()) {
      filters.name = debouncedSearchTerm.trim();
    }

    fetchProjects(Object.keys(filters).length > 0 ? filters : undefined);
  }, [
    typeFilter,
    isActiveFilter,
    clusterFilter,
    debouncedSearchTerm,
    selectedWorkspace?.id,
    fetchProjects,
  ]);

  // Ensure client-side hydration
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Fetch initial data
  useEffect(() => {
    if (isClient) {
      // Pass selectedWorkspace ID to fetchClusters if available
      if (selectedWorkspace?.id) {
        fetchClusters(selectedWorkspace.id);
      } else {
        fetchClusters();
      }
      fetchWorkspaces();
      fetchNamespaceTypes();
      loadProjects();
    }
  }, [
    isClient,
    fetchClusters,
    fetchWorkspaces,
    fetchNamespaceTypes,
    loadProjects,
    selectedWorkspace?.id,
  ]);

  // Reload projects when filters change (including debounced search)
  useEffect(() => {
    if (isClient) {
      loadProjects();
    }
  }, [isClient, loadProjects]);

  const toggleRowExpansion = (projectId: number) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(projectId)) {
      newExpanded.delete(projectId);
    } else {
      newExpanded.add(projectId);
    }
    setExpandedRows(newExpanded);
  };

  const handleToggleActive = (
    projectId: number,
    projectName: string,
    isActive: boolean
  ) => {
    setPendingToggle({ projectId, projectName, isActive });
    setConfirmActiveToggleOpen(true);
  };

  const confirmToggleActive = async () => {
    if (!pendingToggle) {
      return;
    }

    try {
      const response = await updateProjectActive(pendingToggle.projectId, {
        is_active: pendingToggle.isActive,
      });

      if (response?.status) {
        toast.success(
          `Project "${pendingToggle.projectName}" ${pendingToggle.isActive ? 'activated' : 'deactivated'} successfully`
        );
        // Re-fetch projects to get updated data
        loadProjects();
      } else {
        toast.error('Failed to update project status');
      }
    } catch (error) {
      console.error('Error updating project status:', error);
      toast.error('Failed to update project status');
    } finally {
      setConfirmActiveToggleOpen(false);
      setPendingToggle(null);
    }
  };

  const handleEditProject = (projectId: number) => {
    router.push(`/projects/${projectId}`);
  };

  const handleDeleteProject = (projectId: number, projectName: string) => {
    setDeletingProject({ id: projectId, name: projectName });
    setConfirmDeleteOpen(true);
  };

  const confirmDeleteProject = async () => {
    if (!deletingProject) {
      return;
    }

    try {
      const response = await deleteProject(deletingProject.id);
      if (response?.status) {
        toast.success(`Project "${deletingProject.name}" deleted successfully`);
        // Re-fetch projects to get updated data
        loadProjects();
      } else {
        toast.error('Failed to delete project');
      }
    } catch (error) {
      console.error('Error deleting project:', error);
      toast.error('Failed to delete project');
    } finally {
      setConfirmDeleteOpen(false);
      setDeletingProject(null);
    }
  };

  const clearFilters = () => {
    setTypeFilter('');
    setIsActiveFilter(undefined);
    setClusterFilter(undefined);
    setSearchTerm('');
  };

  const hasActiveFilters =
    typeFilter ||
    isActiveFilter !== undefined ||
    clusterFilter ||
    searchTerm.trim();

  // Don't render interactive elements until client-side hydration is complete
  if (!isClient) {
    return (
      <div className='flex flex-1 flex-col'>
        <MainHeader />
        <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
          <div className='space-y-2 mb-4'>
            <h1 className='text-3xl font-medium tracking-tight flex items-center gap-2'>
              <Folder className='h-8 w-8' />
              Projects
            </h1>
            <p className='text-muted-foreground'>
              Manage your projects and track their progress.
            </p>
          </div>

          {/* Loading skeleton for filters */}
          <div className='flex flex-col lg:flex-row gap-4 mb-4'>
            <div className='h-10 bg-muted animate-pulse rounded-md flex-1'></div>
            <div className='h-10 bg-muted animate-pulse rounded-md w-32'></div>
            <div className='h-10 bg-muted animate-pulse rounded-md w-32'></div>
            <div className='h-10 bg-muted animate-pulse rounded-md w-32'></div>
            <div className='h-10 bg-muted animate-pulse rounded-md w-32'></div>
          </div>

          {/* Loading skeleton for table */}
          <div className='border rounded-lg'>
            <div className='h-96 bg-muted animate-pulse rounded-md'></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='flex flex-1 flex-col'>
      {/* Header */}
      <MainHeader />
      <Toaster />

      {/* Main Content */}
      <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
        <div className='space-y-2 mb-6'>
          <div className='flex items-center justify-between'>
            <div>
              <h1 className='text-2xl font-semibold tracking-tight flex items-center gap-2'>
                <Folder className='h-6 w-6' />
                Projects
              </h1>
              <p className='text-sm text-muted-foreground mt-1'>
                Manage your projects and track their progress.
              </p>
            </div>
            <Button
              onClick={() => router.push('/projects/create')}
              className='flex items-center gap-2'
            >
              <Plus className='h-4 w-4' />
              Create Project
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className='flex flex-col lg:flex-row gap-4 mb-4'>
          {/* Search */}
          <div className='relative flex-1'>
            <Search className='absolute left-3 top-3 h-4 w-4 text-muted-foreground' />
            <Input
              placeholder='Search projects...'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className='pl-10'
            />
          </div>

          {/* Type Filter */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='outline' className='w-full lg:w-auto'>
                Type: {typeFilter || 'All'}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setTypeFilter('')}>
                All Types
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {namespaceTypes.map(type => (
                <DropdownMenuItem
                  key={type}
                  onClick={() => setTypeFilter(type)}
                >
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Active Status Filter */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='outline' className='w-full lg:w-auto'>
                Status:{' '}
                {isActiveFilter === undefined
                  ? 'All'
                  : isActiveFilter
                    ? 'Active'
                    : 'Inactive'}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setIsActiveFilter(undefined)}>
                All Status
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setIsActiveFilter(true)}>
                Active
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setIsActiveFilter(false)}>
                Inactive
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Cluster Filter */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='outline' className='w-full lg:w-auto'>
                Cluster:{' '}
                {clusterFilter
                  ? clusters?.find(c => c.id === clusterFilter)?.name ||
                    'Unknown'
                  : 'All'}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setClusterFilter(undefined)}>
                All Clusters
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {clusters?.map(cluster => (
                <DropdownMenuItem
                  key={cluster.id}
                  onClick={() => setClusterFilter(cluster.id)}
                >
                  {cluster.name}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Clear Filters */}
          {hasActiveFilters && (
            <Button
              variant='ghost'
              onClick={clearFilters}
              className='w-full lg:w-auto'
            >
              <Filter className='h-4 w-4 mr-2' />
              Clear Filters
            </Button>
          )}
        </div>

        {/* Loading State */}
        {loading && (
          <div className='border rounded-lg'>
            <div className='h-96 bg-muted animate-pulse rounded-md flex items-center justify-center'>
              <p className='text-muted-foreground'>Loading projects...</p>
            </div>
          </div>
        )}

        {/* Table */}
        {!loading && (
          <div className='border rounded-lg'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className='w-12'></TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Slug</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Cluster</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Deployments</TableHead>
                  <TableHead>Services</TableHead>
                  <TableHead>Ingress</TableHead>
                  <TableHead>Active</TableHead>
                  <TableHead className='w-24'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {!projects || projects.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={11}
                      className='text-center py-8 text-muted-foreground'
                    >
                      No projects found.{' '}
                      {hasActiveFilters && 'Try adjusting your filters.'}
                    </TableCell>
                  </TableRow>
                ) : (
                  projects.map(project => {
                    const projectStatus = calculateProjectStatus(project);
                    return (
                      <Fragment key={project.id}>
                        <TableRow>
                          <TableCell>
                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() => toggleRowExpansion(project.id)}
                              className='p-1 h-6 w-6'
                            >
                              {expandedRows.has(project.id) ? (
                                <ChevronDown className='h-4 w-4' />
                              ) : (
                                <ChevronRight className='h-4 w-4' />
                              )}
                            </Button>
                          </TableCell>
                          <TableCell className='font-medium'>
                            {project.name}
                          </TableCell>
                          <TableCell className='text-muted-foreground'>
                            {project.slug}
                          </TableCell>
                          <TableCell>
                            <Badge variant='outline'>{project.type}</Badge>
                          </TableCell>
                          <TableCell>
                            <div className='flex flex-col gap-1'>
                              <span className='font-medium'>
                                {project.cluster.name}
                              </span>
                              <span className='text-xs text-muted-foreground'>
                                {project.cluster.region} {'•'}{' '}
                                {project.cluster.size}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            {project.type !== 'template' && (
                              <Badge variant={getBadgeVariant(projectStatus)}>
                                {projectStatus}
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            <Badge variant='outline'>
                              {project.deployment_count}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant='outline'>
                              {project.service_count}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant='outline'>
                              {project.ingress_count}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {project.type !== 'template' && (
                              <div className='flex items-center gap-2'>
                                <Switch
                                  checked={project.is_active}
                                  onCheckedChange={checked =>
                                    handleToggleActive(
                                      project.id,
                                      project.name,
                                      checked
                                    )
                                  }
                                  disabled={
                                    updatingActive || projectStatus !== 'active'
                                  }
                                />
                                {updatingActive &&
                                  pendingToggle?.projectId === project.id && (
                                    <span className='text-xs text-muted-foreground'>
                                      Updating...
                                    </span>
                                  )}
                              </div>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className='flex gap-1'>
                              <Button
                                variant='ghost'
                                size='sm'
                                onClick={() => handleEditProject(project.id)}
                                className='p-1 h-6 w-6'
                              >
                                <Cog className='h-3 w-3' />
                              </Button>
                              <Button
                                variant='ghost'
                                size='sm'
                                onClick={() =>
                                  handleDeleteProject(project.id, project.name)
                                }
                                className='p-1 h-6 w-6 text-destructive hover:text-destructive'
                                disabled={deleting}
                              >
                                <Trash2 className='h-3 w-3' />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                        {expandedRows.has(project.id) && (
                          <TableRow>
                            <TableCell colSpan={11} className='p-0'>
                              <div className='bg-muted/20 p-6 space-y-6'>
                                {/* Project Details Header */}
                                <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm'>
                                  <div>
                                    <span className='font-medium'>
                                      Created:
                                    </span>
                                    <div className='text-muted-foreground'>
                                      {new Date(
                                        project.created_at
                                      ).toLocaleDateString()}
                                    </div>
                                  </div>
                                  <div>
                                    <span className='font-medium'>
                                      Updated:
                                    </span>
                                    <div className='text-muted-foreground'>
                                      {new Date(
                                        project.updated_at
                                      ).toLocaleDateString()}
                                    </div>
                                  </div>
                                  <div>
                                    <span className='font-medium'>
                                      Cluster Status:
                                    </span>
                                    <div>
                                      <Badge
                                        variant={getBadgeVariant(
                                          project.cluster.status
                                        )}
                                      >
                                        {project.cluster.status.name}
                                      </Badge>
                                    </div>
                                  </div>
                                  <div>
                                    <span className='font-medium'>
                                      Node Count:
                                    </span>
                                    <div className='text-muted-foreground'>
                                      {project.cluster.node_count} nodes
                                    </div>
                                  </div>
                                </div>

                                {/* Deployments Section */}
                                {project.deployments &&
                                  project.deployments.length > 0 && (
                                    <div>
                                      <div className='flex items-center gap-2 mb-3'>
                                        <Container className='h-4 w-4' />
                                        <span className='font-medium text-sm'>
                                          Deployments (
                                          {project.deployments.length})
                                        </span>
                                      </div>
                                      <div className='space-y-3'>
                                        {project.deployments.map(deployment => (
                                          <div
                                            key={deployment.id}
                                            className='bg-background border rounded-lg p-4'
                                          >
                                            <div className='flex items-center justify-between mb-2'>
                                              <span className='font-medium'>
                                                {deployment.name}
                                              </span>
                                              {project.type !== 'template' && (
                                                <Badge
                                                  variant={getBadgeVariant(
                                                    deployment.status
                                                  )}
                                                >
                                                  {deployment.status.name}
                                                </Badge>
                                              )}
                                            </div>
                                            <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-muted-foreground'>
                                              <div>
                                                <span className='font-medium'>
                                                  Image:
                                                </span>
                                                <div>{deployment.image}</div>
                                              </div>
                                              <div>
                                                <span className='font-medium'>
                                                  Port:
                                                </span>
                                                <div>
                                                  {deployment.container_port}
                                                </div>
                                              </div>
                                              <div>
                                                <span className='font-medium'>
                                                  Replicas:
                                                </span>
                                                <div>{deployment.replicas}</div>
                                              </div>
                                            </div>
                                            {deployment.environments &&
                                              deployment.environments.length >
                                                0 && (
                                                <div className='mt-3'>
                                                  <span className='font-medium text-sm'>
                                                    Environment Variables:
                                                  </span>
                                                  <div className='flex flex-wrap gap-2 mt-2'>
                                                    {deployment.environments.map(
                                                      env => (
                                                        <Badge
                                                          key={env.id}
                                                          variant='outline'
                                                          className='text-xs'
                                                        >
                                                          {env.name}={env.value}
                                                        </Badge>
                                                      )
                                                    )}
                                                  </div>
                                                </div>
                                              )}
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}

                                {/* Services Section */}
                                {project.services &&
                                  project.services.length > 0 && (
                                    <div>
                                      <div className='flex items-center gap-2 mb-3'>
                                        <Server className='h-4 w-4' />
                                        <span className='font-medium text-sm'>
                                          Services ({project.services.length})
                                        </span>
                                      </div>
                                      <div className='space-y-3'>
                                        {project.services.map(service => (
                                          <div
                                            key={service.id}
                                            className='bg-background border rounded-lg p-4'
                                          >
                                            <div className='flex items-center justify-between mb-2'>
                                              <span className='font-medium'>
                                                {service.name}
                                              </span>
                                              {project.type !== 'template' && (
                                                <Badge
                                                  variant={getBadgeVariant(
                                                    service.status
                                                  )}
                                                >
                                                  {service.status.name}
                                                </Badge>
                                              )}
                                            </div>
                                            <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-muted-foreground'>
                                              <div>
                                                <span className='font-medium'>
                                                  Type:
                                                </span>
                                                <div>{service.type}</div>
                                              </div>
                                              <div>
                                                <span className='font-medium'>
                                                  Port:
                                                </span>
                                                <div>
                                                  {service.port} →{' '}
                                                  {service.target_port}
                                                </div>
                                              </div>
                                              {service.cluster_ip && (
                                                <div>
                                                  <span className='font-medium'>
                                                    Cluster IP:
                                                  </span>
                                                  <div>
                                                    {service.cluster_ip}
                                                  </div>
                                                </div>
                                              )}
                                              {service.external_ip && (
                                                <div>
                                                  <span className='font-medium'>
                                                    External IP:
                                                  </span>
                                                  <div>
                                                    {service.external_ip}
                                                  </div>
                                                </div>
                                              )}
                                            </div>
                                            {project.type !== 'template' &&
                                              service.ingress_specs &&
                                              service.ingress_specs.length >
                                                0 && (
                                                <div className='mt-3'>
                                                  <span className='font-medium text-sm'>
                                                    Ingress Specs:
                                                  </span>
                                                  <div className='space-y-1 mt-2'>
                                                    {service.ingress_specs.map(
                                                      spec => (
                                                        <div
                                                          key={spec.id}
                                                          className='flex items-center gap-2 text-sm'
                                                        >
                                                          <span className='text-blue-600'>
                                                            {spec.host}
                                                            {spec.path}
                                                          </span>
                                                          <span className='text-muted-foreground'>
                                                            :{spec.port}
                                                          </span>
                                                        </div>
                                                      )
                                                    )}
                                                  </div>
                                                </div>
                                              )}
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}

                                {/* Ingress Section */}
                                {project.ingress &&
                                  project.ingress.length > 0 && (
                                    <div>
                                      <div className='flex items-center gap-2 mb-3'>
                                        <Globe className='h-4 w-4' />
                                        <span className='font-medium text-sm'>
                                          Ingress ({project.ingress.length})
                                        </span>
                                      </div>
                                      <div className='space-y-3'>
                                        {project.ingress.map(ingress => (
                                          <div
                                            key={ingress.id}
                                            className='bg-background border rounded-lg p-4'
                                          >
                                            <div className='flex items-center justify-between mb-2'>
                                              <span className='font-medium'>
                                                {ingress.name}
                                              </span>
                                              {project.type !== 'template' && (
                                                <Badge
                                                  variant={getBadgeVariant(
                                                    ingress.status
                                                  )}
                                                >
                                                  {ingress.status.name}
                                                </Badge>
                                              )}
                                            </div>

                                            <div className='mb-3'>
                                              <span className='font-medium text-sm'>
                                                Class:
                                              </span>
                                              <span className='ml-2 text-muted-foreground'>
                                                {ingress.class}
                                              </span>
                                            </div>
                                            {project.type !== 'template' &&
                                              ingress.ingress_specs &&
                                              ingress.ingress_specs.length >
                                                0 && (
                                                <div>
                                                  <span className='font-medium text-sm'>
                                                    Routes:
                                                  </span>
                                                  <div className='space-y-2 mt-2'>
                                                    {ingress.ingress_specs.map(
                                                      spec => (
                                                        <div
                                                          key={spec.id}
                                                          className='bg-muted/30 rounded p-3'
                                                        >
                                                          <div className='flex items-center gap-2 mb-2'>
                                                            <a
                                                              href={`https://${spec.host}${spec.path}`}
                                                              target='_blank'
                                                              rel='noopener noreferrer'
                                                              className='text-blue-600 hover:text-blue-800 flex items-center gap-1'
                                                            >
                                                              {spec.host}
                                                              {spec.path}
                                                              <ExternalLink className='h-3 w-3' />
                                                            </a>
                                                            <span className='text-muted-foreground'>
                                                              :{spec.port}
                                                            </span>
                                                          </div>
                                                          {spec.service && (
                                                            <div className='text-sm text-muted-foreground'>
                                                              → Service:{' '}
                                                              {
                                                                spec.service
                                                                  .name
                                                              }{' '}
                                                              (
                                                              {
                                                                spec.service
                                                                  .port
                                                              }
                                                              )
                                                            </div>
                                                          )}
                                                        </div>
                                                      )
                                                    )}
                                                  </div>
                                                </div>
                                              )}
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                              </div>
                            </TableCell>
                          </TableRow>
                        )}
                      </Fragment>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </div>
        )}

        {/* Project Active Toggle Confirmation Modal */}
        <ConfirmActionModal
          open={confirmActiveToggleOpen}
          onOpenChange={setConfirmActiveToggleOpen}
          onConfirm={confirmToggleActive}
          title={`${pendingToggle?.isActive ? 'Activate' : 'Deactivate'} Project`}
          description={`Are you sure you want to ${
            pendingToggle?.isActive ? 'activate' : 'deactivate'
          } the project "${pendingToggle?.projectName}"? This will change the project's operational status.`}
          actionText={pendingToggle?.isActive ? 'Activate' : 'Deactivate'}
          loadingText={
            pendingToggle?.isActive ? 'Activating...' : 'Deactivating...'
          }
          variant={pendingToggle?.isActive ? 'default' : 'destructive'}
          icon={
            pendingToggle?.isActive ? (
              <Activity className='h-5 w-5 text-green-600' />
            ) : (
              <Activity className='h-5 w-5 text-red-600' />
            )
          }
        />

        {/* Project Delete Confirmation Modal */}
        <ConfirmActionModal
          open={confirmDeleteOpen}
          onOpenChange={setConfirmDeleteOpen}
          onConfirm={confirmDeleteProject}
          title='Delete Project'
          description={`Are you sure you want to delete the project "${deletingProject?.name}"? This action cannot be undone and will permanently remove the project and all its associated resources.`}
          actionText='Delete'
          loadingText='Deleting...'
          variant='destructive'
          icon={<Trash2 className='h-5 w-5 text-red-600' />}
        />
      </div>
    </div>
  );
}
